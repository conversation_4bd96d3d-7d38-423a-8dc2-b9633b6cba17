# Network Analysis for Himalayan Lakes Microbial Communities
# This script analyzes bacterial and fungal ASV tables to create network visualizations
# at the Family taxonomic level, with separate analyses for different sample types and regions

# Load required libraries
library(readxl)
library(igraph)
library(vegan)
library(tidyverse)
library(RColorBrewer)

# Configuration: Choose taxonomic level for analysis
# The bacteria taxonomy table appears to have specific taxonomic names rather than standard levels
# Let's use the 6th column (index 6) which appears to be at family level for bacteria
TAXONOMIC_LEVEL_FUNGI <- "Family"  # Standard family level for fungi
BACTERIA_TAXONOMIC_COLUMN <- 6  # 6th column appears to be family-level for bacteria

cat("Performing network analysis at Family level\n")
cat("This provides optimal balance between computational efficiency and biological resolution\n")

# Read data files
cat("========== LOADING DATA FILES ==========\n")

# Bacteria datasets
bacteria_asv <- read_excel("ASV_table_bacteria.xlsx")
bacteria_tax <- read_excel("taxatable_bacteria.xlsx")
bacteria_meta <- read_excel("metadata_bacteria.xlsx")

# Fungi datasets  
fungi_asv <- read_excel("ASV_table_fungi.xlsx")
fungi_tax <- read_excel("taxatable_fungi.xlsx")
fungi_meta <- read_excel("metadata_fungi.xlsx")

# Print data dimensions for debugging
cat("Original data dimensions:\n")
cat("Bacteria ASV table:", dim(bacteria_asv), "\n")
cat("Bacteria taxonomy:", dim(bacteria_tax), "\n")
cat("Fungi ASV table:", dim(fungi_asv), "\n")
cat("Fungi taxonomy:", dim(fungi_tax), "\n")

# Function to aggregate ASVs to specified taxonomic level
aggregate_to_taxonomic_level <- function(asv_table, taxonomy_table, taxonomic_level) {
  cat("\n--- Aggregating to", taxonomic_level, "level ---\n")
  
  # Check if taxonomic level exists in taxonomy table
  if (!taxonomic_level %in% colnames(taxonomy_table)) {
    stop("Taxonomic level '", taxonomic_level, "' not found in taxonomy table")
  }
  
  # Get the first column name (ASV ID column)
  asv_id_col <- colnames(asv_table)[1]
  tax_asv_id_col <- colnames(taxonomy_table)[1]
  
  # Merge ASV table with taxonomy
  merged_data <- merge(asv_table, taxonomy_table[, c(tax_asv_id_col, taxonomic_level)], 
                     by.x = asv_id_col, by.y = tax_asv_id_col, all.x = TRUE)
  
  # Remove rows with NA or empty taxonomic assignment
  valid_rows <- !is.na(merged_data[[taxonomic_level]]) & 
              merged_data[[taxonomic_level]] != "" & 
              merged_data[[taxonomic_level]] != "NA"
  
  if (sum(valid_rows) == 0) {
    stop("No valid taxonomic assignments found at ", taxonomic_level, " level")
  }
  
  cat("Removed", sum(!valid_rows), "ASVs with missing", taxonomic_level, "assignment\n")
  merged_data <- merged_data[valid_rows, ]
  
  # Get sample columns (all columns except ASV ID and taxonomic level)
  sample_cols <- setdiff(colnames(merged_data), c(asv_id_col, taxonomic_level))
  
  # Aggregate by summing ASV counts within each taxonomic group
  aggregated <- merged_data %>%
    group_by(!!sym(taxonomic_level)) %>%
    summarise(across(all_of(sample_cols), sum, na.rm = TRUE), .groups = 'drop')
  
  # Rename the taxonomic level column to match original ASV ID column name
  colnames(aggregated)[1] <- asv_id_col
  
  cat("Aggregated from", nrow(asv_table), "ASVs to", nrow(aggregated), taxonomic_level, "groups\n")
  
  return(as.data.frame(aggregated))
}

# Function to filter samples by metadata criteria
filter_samples_by_metadata <- function(asv_table, metadata, filter_column, filter_value) {
  # Determine the sample ID column name
  sample_id_col <- NULL
  if ("Sample" %in% colnames(metadata)) {
    sample_id_col <- "Sample"
  } else if ("Sample_ID" %in% colnames(metadata)) {
    sample_id_col <- "Sample_ID"
  } else if ("sample" %in% colnames(metadata)) {
    sample_id_col <- "sample"
  } else {
    cat("Error: Could not find sample ID column in metadata\n")
    return(NULL)
  }
  
  # Get sample IDs for the specified filter
  target_samples <- metadata[[sample_id_col]][metadata[[filter_column]] == filter_value]
  
  cat("Found", length(target_samples), "samples for", filter_column, "=", filter_value, "\n")
  
  # Find which columns in ASV table correspond to these samples
  sample_cols <- which(colnames(asv_table) %in% target_samples)
  
  if (length(sample_cols) == 0) {
    cat("Warning: No samples found for", filter_column, "=", filter_value, "\n")
    return(NULL)
  }
  
  # Return ASV table with only the target samples (plus the first column with IDs)
  filtered_table <- asv_table[, c(1, sample_cols)]
  cat("Filtered", filter_column, "=", filter_value, "samples:", ncol(filtered_table) - 1, "samples\n")
  return(filtered_table)
}

# Aggregate all datasets to the specified taxonomic level
cat("\n========== AGGREGATING TO TAXONOMIC LEVEL ==========\n")

bacteria_agg <- aggregate_to_taxonomic_level(bacteria_asv, bacteria_tax, TAXONOMIC_LEVEL)
fungi_agg <- aggregate_to_taxonomic_level(fungi_asv, fungi_tax, TAXONOMIC_LEVEL)

cat("\nAggregated data dimensions:\n")
cat("Bacteria aggregated:", dim(bacteria_agg), "\n")
cat("Fungi aggregated:", dim(fungi_agg), "\n")

# Create filtered datasets by Sample_type
cat("\n========== FILTERING BY SAMPLE_TYPE ==========\n")

# Bacteria by Sample_type
bacteria_sediment <- filter_samples_by_metadata(bacteria_agg, bacteria_meta, "Sample_type", "Sediment")
bacteria_water <- filter_samples_by_metadata(bacteria_agg, bacteria_meta, "Sample_type", "Water")

# Fungi by Sample_type
fungi_sediment <- filter_samples_by_metadata(fungi_agg, fungi_meta, "Sample_type", "Sediment")
fungi_water <- filter_samples_by_metadata(fungi_agg, fungi_meta, "Sample_type", "Water")

# Create filtered datasets by Region
cat("\n========== FILTERING BY REGION ==========\n")

# Bacteria by Region
bacteria_western <- filter_samples_by_metadata(bacteria_agg, bacteria_meta, "Region", "Western Himalayas")
bacteria_eastern <- filter_samples_by_metadata(bacteria_agg, bacteria_meta, "Region", "Eastern Himalayas")

# Fungi by Region
fungi_western <- filter_samples_by_metadata(fungi_agg, fungi_meta, "Region", "Western Himalayas")
fungi_eastern <- filter_samples_by_metadata(fungi_agg, fungi_meta, "Region", "Eastern Himalayas")

cat("\n========== SUMMARY OF FILTERED DATASETS ==========\n")
if (!is.null(bacteria_sediment)) cat("Bacteria Sediment:", dim(bacteria_sediment), "\n")
if (!is.null(bacteria_water)) cat("Bacteria Water:", dim(bacteria_water), "\n")
if (!is.null(fungi_sediment)) cat("Fungi Sediment:", dim(fungi_sediment), "\n")
if (!is.null(fungi_water)) cat("Fungi Water:", dim(fungi_water), "\n")
if (!is.null(bacteria_western)) cat("Bacteria Western:", dim(bacteria_western), "\n")
if (!is.null(bacteria_eastern)) cat("Bacteria Eastern:", dim(bacteria_eastern), "\n")
if (!is.null(fungi_western)) cat("Fungi Western:", dim(fungi_western), "\n")
if (!is.null(fungi_eastern)) cat("Fungi Eastern:", dim(fungi_eastern), "\n")

cat("\nData aggregation and filtering completed successfully!\n")

# Function to create correlation matrix for taxonomic-level data
create_taxonomic_correlation_matrix <- function(taxonomic_table, threshold = 0.6) {
  cat("\n--- Creating correlation matrix ---\n")
  cat("Input dimensions:", dim(taxonomic_table), "\n")

  # Check if the table has at least one column besides the taxonomic IDs
  if (ncol(taxonomic_table) <= 1) {
    stop("Table must have at least one sample column besides the taxonomic IDs")
  }

  tryCatch({
    # Extract abundance matrix (remove first column which contains taxonomic names)
    abundance_matrix <- as.matrix(taxonomic_table[,-1])
    rownames(abundance_matrix) <- taxonomic_table[[1]]

    # Check for rows with all zeros or constant values
    row_sums <- rowSums(abundance_matrix)
    constant_rows <- row_sums == 0 | apply(abundance_matrix, 1, function(x) length(unique(x)) <= 1)

    if (any(constant_rows)) {
      cat("Removing", sum(constant_rows), "constant rows (all zeros or all same value)\n")
      abundance_matrix <- abundance_matrix[!constant_rows, , drop = FALSE]
    }

    # Check if we still have enough rows
    if (nrow(abundance_matrix) <= 1) {
      stop("Not enough variable rows for correlation analysis after removing constant rows")
    }

    cat("Final matrix for correlation:", nrow(abundance_matrix), "taxa ×", ncol(abundance_matrix), "samples\n")

    # Calculate correlations
    cat("Calculating Spearman correlations...\n")
    cor_matrix <- cor(t(abundance_matrix), method = "spearman", use = "pairwise.complete.obs")

    # Replace any NAs with 0
    if (any(is.na(cor_matrix))) {
      cat("Replacing", sum(is.na(cor_matrix)), "NA values with 0\n")
      cor_matrix[is.na(cor_matrix)] <- 0
    }

    # Print summary before thresholding
    cat("\nBefore thresholding:\n")
    cat("Total correlations:", length(cor_matrix), "\n")
    cat("Positive correlations:", sum(cor_matrix > 0), "\n")
    cat("Negative correlations:", sum(cor_matrix < 0), "\n")
    cat("Range:", range(cor_matrix), "\n")

    # Store signs before thresholding
    signs <- sign(cor_matrix)

    # Apply threshold to absolute values
    cor_matrix[abs(cor_matrix) < threshold] <- 0

    # Restore signs
    cor_matrix <- cor_matrix * signs

    # Set diagonal to zero
    diag(cor_matrix) <- 0

    # Print summary after thresholding
    cat("\nAfter thresholding (|correlation| ≥", threshold, "):\n")
    cat("Total non-zero correlations:", sum(cor_matrix != 0), "\n")
    cat("Positive correlations:", sum(cor_matrix > 0), "\n")
    cat("Negative correlations:", sum(cor_matrix < 0), "\n")

    if (sum(cor_matrix != 0) > 0) {
      cat("Range of non-zero correlations:", range(cor_matrix[cor_matrix != 0]), "\n")
    } else {
      cat("No correlations above threshold.\n")
    }

    return(list(
      cor_matrix = cor_matrix,
      taxa_names = rownames(abundance_matrix),
      n_taxa = nrow(abundance_matrix)
    ))

  }, error = function(e) {
    cat("Error in correlation calculation:", e$message, "\n")
    return(NULL)
  })
}

# Function to analyze taxonomic network
analyze_taxonomic_network <- function(cor_result, group_name) {
  if (is.null(cor_result)) {
    cat("Cannot analyze network: correlation result is NULL\n")
    return(NULL)
  }

  cat("\n========== ANALYZING", group_name, "NETWORK ==========\n")

  cor_matrix <- cor_result$cor_matrix
  taxa_names <- cor_result$taxa_names

  # Check if we have any correlations
  if (sum(cor_matrix != 0) == 0) {
    cat("No correlations above threshold. Cannot create network.\n")
    return(NULL)
  }

  tryCatch({
    # Create igraph network from correlation matrix
    network <- graph_from_adjacency_matrix(
      abs(cor_matrix),
      mode = "undirected",
      weighted = TRUE,
      diag = FALSE
    )

    # Add taxa names as vertex names
    V(network)$name <- taxa_names

    # Get edge list to properly assign edge attributes
    edge_list <- as_edgelist(network, names = FALSE)

    # Extract correlation values for each edge
    edge_correlations <- numeric(nrow(edge_list))
    edge_signs <- character(nrow(edge_list))

    for (i in 1:nrow(edge_list)) {
      row_idx <- edge_list[i, 1]
      col_idx <- edge_list[i, 2]
      correlation_value <- cor_matrix[row_idx, col_idx]
      edge_correlations[i] <- correlation_value
      edge_signs[i] <- ifelse(correlation_value > 0, "positive", "negative")
    }

    # Add edge attributes
    E(network)$sign <- edge_signs
    E(network)$correlation <- edge_correlations

    # Calculate network metrics
    cat("Network created with", vcount(network), "nodes and", ecount(network), "edges\n")

    # Node degree
    node_degrees <- degree(network)
    cat("Average degree:", mean(node_degrees), "\n")
    cat("Max degree:", max(node_degrees), "\n")

    # Centrality measures
    betweenness <- betweenness(network)
    closeness <- closeness(network)

    # Community detection
    communities <- cluster_louvain(network)
    community_count <- length(communities)
    modularity_score <- modularity(communities)

    cat("Communities detected:", community_count, "\n")
    cat("Modularity:", modularity_score, "\n")

    # Identify hub nodes (top 10% by degree)
    degree_threshold <- quantile(node_degrees, 0.9)
    hub_nodes <- names(node_degrees[node_degrees >= degree_threshold])
    cat("Hub nodes (top 10% by degree):", length(hub_nodes), "\n")

    # Create results list
    results <- list(
      network = network,
      node_degrees = node_degrees,
      betweenness = betweenness,
      closeness = closeness,
      communities = communities,
      community_count = community_count,
      modularity = modularity_score,
      hub_nodes = hub_nodes,
      taxa_names = taxa_names,
      group_name = group_name
    )

    return(results)

  }, error = function(e) {
    cat("Error in network analysis:", e$message, "\n")
    return(NULL)
  })
}

# Function to create cross-correlation matrix between two taxonomic tables
create_taxonomic_cross_correlation_matrix <- function(table1, table2, threshold = 0.6) {
  cat("\n--- Creating cross-correlation matrix ---\n")
  cat("Table 1 dimensions:", dim(table1), "\n")
  cat("Table 2 dimensions:", dim(table2), "\n")

  tryCatch({
    # Extract abundance matrices (remove first column which contains taxonomic names)
    abundance_matrix1 <- as.matrix(table1[,-1])
    abundance_matrix2 <- as.matrix(table2[,-1])

    rownames(abundance_matrix1) <- table1[[1]]
    rownames(abundance_matrix2) <- table2[[1]]

    # Remove constant rows from both matrices
    row_sums1 <- rowSums(abundance_matrix1)
    constant_rows1 <- row_sums1 == 0 | apply(abundance_matrix1, 1, function(x) length(unique(x)) <= 1)
    if (any(constant_rows1)) {
      cat("Removing", sum(constant_rows1), "constant rows from table 1\n")
      abundance_matrix1 <- abundance_matrix1[!constant_rows1, , drop = FALSE]
    }

    row_sums2 <- rowSums(abundance_matrix2)
    constant_rows2 <- row_sums2 == 0 | apply(abundance_matrix2, 1, function(x) length(unique(x)) <= 1)
    if (any(constant_rows2)) {
      cat("Removing", sum(constant_rows2), "constant rows from table 2\n")
      abundance_matrix2 <- abundance_matrix2[!constant_rows2, , drop = FALSE]
    }

    # Check if we have enough data
    if (nrow(abundance_matrix1) <= 1 || nrow(abundance_matrix2) <= 1) {
      stop("Not enough variable rows for cross-correlation analysis")
    }

    # Get sample names and find common samples
    samples1 <- colnames(abundance_matrix1)
    samples2 <- colnames(abundance_matrix2)
    common_samples <- intersect(samples1, samples2)

    if (length(common_samples) == 0) {
      stop("No common samples found between the two tables")
    }

    cat("Found", length(common_samples), "common samples\n")

    # Subset to common samples
    abundance_matrix1 <- abundance_matrix1[, common_samples, drop = FALSE]
    abundance_matrix2 <- abundance_matrix2[, common_samples, drop = FALSE]

    cat("Final matrices: ", nrow(abundance_matrix1), "×", ncol(abundance_matrix1),
        " vs ", nrow(abundance_matrix2), "×", ncol(abundance_matrix2), "\n")

    # Calculate cross-correlations
    cat("Calculating cross-correlations...\n")
    cross_cor_matrix <- cor(t(abundance_matrix1), t(abundance_matrix2),
                           method = "spearman", use = "pairwise.complete.obs")

    # Replace any NAs with 0
    if (any(is.na(cross_cor_matrix))) {
      cat("Replacing", sum(is.na(cross_cor_matrix)), "NA values with 0\n")
      cross_cor_matrix[is.na(cross_cor_matrix)] <- 0
    }

    # Print summary before thresholding
    cat("\nBefore thresholding:\n")
    cat("Total correlations:", length(cross_cor_matrix), "\n")
    cat("Positive correlations:", sum(cross_cor_matrix > 0), "\n")
    cat("Negative correlations:", sum(cross_cor_matrix < 0), "\n")
    cat("Range:", range(cross_cor_matrix), "\n")

    # Apply threshold
    cross_cor_matrix[abs(cross_cor_matrix) < threshold] <- 0

    # Print summary after thresholding
    cat("\nAfter thresholding (|correlation| ≥", threshold, "):\n")
    cat("Total non-zero correlations:", sum(cross_cor_matrix != 0), "\n")
    cat("Positive correlations:", sum(cross_cor_matrix > 0), "\n")
    cat("Negative correlations:", sum(cross_cor_matrix < 0), "\n")

    if (sum(cross_cor_matrix != 0) > 0) {
      cat("Range of non-zero correlations:", range(cross_cor_matrix[cross_cor_matrix != 0]), "\n")
    } else {
      cat("No correlations above threshold.\n")
    }

    return(list(
      cor_matrix = cross_cor_matrix,
      taxa_names1 = rownames(abundance_matrix1),
      taxa_names2 = rownames(abundance_matrix2),
      common_samples = common_samples
    ))

  }, error = function(e) {
    cat("Error in cross-correlation calculation:", e$message, "\n")
    return(NULL)
  })
}

# Function to analyze cross-correlation network
analyze_taxonomic_cross_network <- function(cross_cor_result, group1_name, group2_name) {
  if (is.null(cross_cor_result)) {
    cat("Cannot analyze cross-network: correlation result is NULL\n")
    return(NULL)
  }

  cat("\n========== ANALYZING", group1_name, "vs", group2_name, "CROSS-NETWORK ==========\n")

  cross_cor_matrix <- cross_cor_result$cor_matrix
  taxa_names1 <- cross_cor_result$taxa_names1
  taxa_names2 <- cross_cor_result$taxa_names2

  # Check if we have any correlations
  if (sum(cross_cor_matrix != 0) == 0) {
    cat("No correlations above threshold. Cannot create cross-network.\n")
    return(NULL)
  }

  tryCatch({
    # Create bipartite network
    # First, create an edge list from the correlation matrix
    edges <- which(cross_cor_matrix != 0, arr.ind = TRUE)
    edge_list <- data.frame(
      from = taxa_names1[edges[,1]],
      to = taxa_names2[edges[,2]],
      weight = abs(cross_cor_matrix[edges]),
      correlation = cross_cor_matrix[edges],
      sign = ifelse(cross_cor_matrix[edges] > 0, "positive", "negative"),
      stringsAsFactors = FALSE
    )

    # Create igraph network
    network <- graph_from_data_frame(edge_list, directed = FALSE)

    # Add vertex attributes to distinguish between the two groups
    V(network)$type <- ifelse(V(network)$name %in% taxa_names1, group1_name, group2_name)

    # Calculate network metrics
    cat("Cross-network created with", vcount(network), "nodes and", ecount(network), "edges\n")
    cat("Group 1 (", group1_name, ") nodes:", sum(V(network)$type == group1_name), "\n")
    cat("Group 2 (", group2_name, ") nodes:", sum(V(network)$type == group2_name), "\n")

    # Node degree
    node_degrees <- degree(network)
    cat("Average degree:", mean(node_degrees), "\n")
    cat("Max degree:", max(node_degrees), "\n")

    # Centrality measures
    betweenness <- betweenness(network)
    closeness <- closeness(network)

    # Community detection
    communities <- cluster_louvain(network)
    community_count <- length(communities)
    modularity_score <- modularity(communities)

    cat("Communities detected:", community_count, "\n")
    cat("Modularity:", modularity_score, "\n")

    # Identify hub nodes (top 10% by degree)
    degree_threshold <- quantile(node_degrees, 0.9)
    hub_nodes <- names(node_degrees[node_degrees >= degree_threshold])
    cat("Hub nodes (top 10% by degree):", length(hub_nodes), "\n")

    # Create results list
    results <- list(
      network = network,
      node_degrees = node_degrees,
      betweenness = betweenness,
      closeness = closeness,
      communities = communities,
      community_count = community_count,
      modularity = modularity_score,
      hub_nodes = hub_nodes,
      edge_list = edge_list,
      group1_name = group1_name,
      group2_name = group2_name
    )

    return(results)

  }, error = function(e) {
    cat("Error in cross-network analysis:", e$message, "\n")
    return(NULL)
  })
}

# Function to plot individual networks
plot_individual_network <- function(network_result, group_name, taxonomic_level) {
  if (is.null(network_result)) {
    cat("Cannot plot network: network result is NULL\n")
    return(NULL)
  }

  tryCatch({
    network <- network_result$network

    # Set up layout
    layout <- layout_with_fr(network)

    # Color nodes by community with muted, transparent colors
    # Create a palette of muted, professional colors with transparency
    if (network_result$community_count <= 12) {
      # Use a predefined set of muted colors for small numbers of communities
      muted_colors <- c("#8DD3C7", "#FFFFB3", "#BEBADA", "#FB8072", "#80B1D3", "#FDB462",
                       "#B3DE69", "#FCCDE5", "#D9D9D9", "#BC80BD", "#CCEBC5", "#FFED6F")
      community_colors <- muted_colors[1:network_result$community_count]
    } else {
      # For larger numbers, use a muted color palette
      community_colors <- rainbow(network_result$community_count, s = 0.6, v = 0.8)
    }
    # Add transparency to node colors (80% opacity)
    node_colors <- adjustcolor(community_colors[membership(network_result$communities)], alpha.f = 0.8)

    # Size nodes by degree
    node_sizes <- (network_result$node_degrees / max(network_result$node_degrees)) * 15 + 5

    # Color edges by correlation sign with muted, transparent colors
    base_edge_colors <- ifelse(E(network)$sign == "positive", "#4682B4", "#CD5C5C")  # Steel blue and Indian red
    edge_colors <- adjustcolor(base_edge_colors, alpha.f = 0.7)  # 70% opacity for edges

    # Create filename
    filename <- paste0(group_name, "_", taxonomic_level, "_network.pdf")

    # Create plot
    pdf(filename, width = 12, height = 10)

    plot(network,
         layout = layout,
         vertex.color = node_colors,
         vertex.size = node_sizes,
         vertex.label = ifelse(network_result$node_degrees >= quantile(network_result$node_degrees, 0.9),
                              V(network)$name, ""),
         vertex.label.cex = 0.7,
         vertex.label.color = "black",
         edge.color = edge_colors,
         edge.width = abs(E(network)$correlation) * 2,
         main = paste(group_name, "Network at", taxonomic_level, "Level"),
         sub = paste("Nodes:", vcount(network), "| Edges:", ecount(network),
                    "| Communities:", network_result$community_count))

    # Add legend
    legend("topright",
           legend = c("Positive correlation", "Negative correlation", "Hub nodes"),
           col = c("#4682B4", "#CD5C5C", "black"),
           lty = c(1, 1, NA),
           pch = c(NA, NA, 16),
           cex = 0.8)

    dev.off()

    cat("Network plot saved:", filename, "\n")

  }, error = function(e) {
    cat("Error creating network plot:", e$message, "\n")
  })
}

# Function to plot cross-correlation networks
plot_cross_network <- function(cross_network_result, group1_name, group2_name, taxonomic_level) {
  if (is.null(cross_network_result)) {
    cat("Cannot plot cross-network: network result is NULL\n")
    return(NULL)
  }

  tryCatch({
    network <- cross_network_result$network

    # Set up layout for bipartite network
    layout <- layout_as_bipartite(network)

    # Color nodes by group with muted, transparent colors
    base_node_colors <- ifelse(V(network)$type == group1_name, "#87CEEB", "#98D982")  # Muted sky blue and sage green
    node_colors <- adjustcolor(base_node_colors, alpha.f = 0.8)  # 80% opacity

    # Size nodes by degree
    node_sizes <- (cross_network_result$node_degrees / max(cross_network_result$node_degrees)) * 12 + 4

    # Color edges by correlation sign with muted, transparent colors
    base_edge_colors <- ifelse(E(network)$sign == "positive", "#4682B4", "#CD5C5C")  # Steel blue and Indian red
    edge_colors <- adjustcolor(base_edge_colors, alpha.f = 0.7)  # 70% opacity for edges

    # Create filename
    filename <- paste0(group1_name, "_vs_", group2_name, "_", taxonomic_level, "_cross_network.pdf")

    # Create plot
    pdf(filename, width = 14, height = 10)

    plot(network,
         layout = layout,
         vertex.color = node_colors,
         vertex.size = node_sizes,
         vertex.label = ifelse(cross_network_result$node_degrees >= quantile(cross_network_result$node_degrees, 0.9),
                              V(network)$name, ""),
         vertex.label.cex = 0.6,
         vertex.label.color = "black",
         edge.color = edge_colors,
         edge.width = abs(E(network)$correlation) * 2,
         main = paste(group1_name, "vs", group2_name, "Cross-Network at", taxonomic_level, "Level"),
         sub = paste("Nodes:", vcount(network), "| Edges:", ecount(network),
                    "| Communities:", cross_network_result$community_count))

    # Add legend
    legend("topright",
           legend = c(group1_name, group2_name, "Positive correlation", "Negative correlation"),
           col = c("#87CEEB", "#98D982", "#4682B4", "#CD5C5C"),
           pch = c(16, 16, NA, NA),
           lty = c(NA, NA, 1, 1),
           cex = 0.8)

    dev.off()

    cat("Cross-network plot saved:", filename, "\n")

  }, error = function(e) {
    cat("Error creating cross-network plot:", e$message, "\n")
  })
}

# Perform individual network analyses
cat("\n\n========== INDIVIDUAL NETWORK ANALYSES ==========\n")

# 1. Single bacterial networks
cat("\n--- BACTERIAL NETWORKS ---\n")

# Total bacterial network
bacteria_total_cor <- create_taxonomic_correlation_matrix(bacteria_agg, threshold = 0.6)
bacteria_total_network <- analyze_taxonomic_network(bacteria_total_cor, "Bacteria_Total")

# Bacterial networks by Sample_type
if (!is.null(bacteria_sediment)) {
  bacteria_sediment_cor <- create_taxonomic_correlation_matrix(bacteria_sediment, threshold = 0.6)
  bacteria_sediment_network <- analyze_taxonomic_network(bacteria_sediment_cor, "Bacteria_Sediment")
}

if (!is.null(bacteria_water)) {
  bacteria_water_cor <- create_taxonomic_correlation_matrix(bacteria_water, threshold = 0.6)
  bacteria_water_network <- analyze_taxonomic_network(bacteria_water_cor, "Bacteria_Water")
}

# Bacterial networks by Region
if (!is.null(bacteria_western)) {
  bacteria_western_cor <- create_taxonomic_correlation_matrix(bacteria_western, threshold = 0.6)
  bacteria_western_network <- analyze_taxonomic_network(bacteria_western_cor, "Bacteria_Western")
}

if (!is.null(bacteria_eastern)) {
  bacteria_eastern_cor <- create_taxonomic_correlation_matrix(bacteria_eastern, threshold = 0.6)
  bacteria_eastern_network <- analyze_taxonomic_network(bacteria_eastern_cor, "Bacteria_Eastern")
}

# 2. Single fungal networks
cat("\n--- FUNGAL NETWORKS ---\n")

# Total fungal network
fungi_total_cor <- create_taxonomic_correlation_matrix(fungi_agg, threshold = 0.6)
fungi_total_network <- analyze_taxonomic_network(fungi_total_cor, "Fungi_Total")

# Fungal networks by Sample_type
if (!is.null(fungi_sediment)) {
  fungi_sediment_cor <- create_taxonomic_correlation_matrix(fungi_sediment, threshold = 0.6)
  fungi_sediment_network <- analyze_taxonomic_network(fungi_sediment_cor, "Fungi_Sediment")
}

if (!is.null(fungi_water)) {
  fungi_water_cor <- create_taxonomic_correlation_matrix(fungi_water, threshold = 0.6)
  fungi_water_network <- analyze_taxonomic_network(fungi_water_cor, "Fungi_Water")
}

# Fungal networks by Region
if (!is.null(fungi_western)) {
  fungi_western_cor <- create_taxonomic_correlation_matrix(fungi_western, threshold = 0.6)
  fungi_western_network <- analyze_taxonomic_network(fungi_western_cor, "Fungi_Western")
}

if (!is.null(fungi_eastern)) {
  fungi_eastern_cor <- create_taxonomic_correlation_matrix(fungi_eastern, threshold = 0.6)
  fungi_eastern_network <- analyze_taxonomic_network(fungi_eastern_cor, "Fungi_Eastern")
}

# Perform cross-correlation analyses (bacteria vs fungi)
cat("\n\n========== CROSS-CORRELATION ANALYSES ==========\n")

# Helper function to perform cross-correlation with multiple thresholds
perform_taxonomic_cross_correlation <- function(table1, table2, group1_name, group2_name) {
  cat("\n\n----- ", group1_name, " vs ", group2_name, " -----\n", sep="")

  # For cross-correlations, start with lower threshold (0.4) since cross-kingdom correlations are typically weaker
  tryCatch({
    cross_cor <- create_taxonomic_cross_correlation_matrix(table1, table2, threshold = 0.4)

    if (!is.null(cross_cor) && sum(cross_cor$cor_matrix != 0) > 0) {
      cat("\nFound correlations above threshold 0.4\n")
      cross_network <- analyze_taxonomic_cross_network(cross_cor, group1_name, group2_name)
      return(cross_network)
    } else {
      cat("\nNo correlations above threshold 0.4. Trying threshold 0.3...\n")

      # Try with threshold 0.3
      cross_cor_03 <- create_taxonomic_cross_correlation_matrix(table1, table2, threshold = 0.3)

      if (!is.null(cross_cor_03) && sum(cross_cor_03$cor_matrix != 0) > 0) {
        cat("\nFound correlations above threshold 0.3\n")
        cross_network <- analyze_taxonomic_cross_network(cross_cor_03,
                                                        paste(group1_name, "(0.3)"),
                                                        paste(group2_name, "(0.3)"))
        return(cross_network)
      } else {
        cat("\nNo correlations above threshold 0.3. Trying threshold 0.25...\n")

        # Try with threshold 0.25
        cross_cor_025 <- create_taxonomic_cross_correlation_matrix(table1, table2, threshold = 0.25)

        if (!is.null(cross_cor_025) && sum(cross_cor_025$cor_matrix != 0) > 0) {
          cat("\nFound correlations above threshold 0.25\n")
          cross_network <- analyze_taxonomic_cross_network(cross_cor_025,
                                                          paste(group1_name, "(0.25)"),
                                                          paste(group2_name, "(0.25)"))
          return(cross_network)
        } else {
          cat("\nNo significant cross-correlations found even at threshold 0.25\n")
          cat("This may indicate weak bacteria-fungi interactions in this environment\n")
          return(NULL)
        }
      }
    }
  }, error = function(e) {
    cat("Error in cross-correlation analysis:", e$message, "\n")
    return(NULL)
  })
}

# 1. Total bacteria vs fungi cross-network
bacteria_fungi_total_network <- perform_taxonomic_cross_correlation(
  bacteria_agg, fungi_agg,
  "Bacteria_Total", "Fungi_Total"
)

# 2. Cross-networks by Sample_type
if (!is.null(bacteria_sediment) && !is.null(fungi_sediment)) {
  bacteria_fungi_sediment_network <- perform_taxonomic_cross_correlation(
    bacteria_sediment, fungi_sediment,
    "Bacteria_Sediment", "Fungi_Sediment"
  )
}

if (!is.null(bacteria_water) && !is.null(fungi_water)) {
  bacteria_fungi_water_network <- perform_taxonomic_cross_correlation(
    bacteria_water, fungi_water,
    "Bacteria_Water", "Fungi_Water"
  )
}

# 3. Cross-networks by Region
if (!is.null(bacteria_western) && !is.null(fungi_western)) {
  bacteria_fungi_western_network <- perform_taxonomic_cross_correlation(
    bacteria_western, fungi_western,
    "Bacteria_Western", "Fungi_Western"
  )
}

if (!is.null(bacteria_eastern) && !is.null(fungi_eastern)) {
  bacteria_fungi_eastern_network <- perform_taxonomic_cross_correlation(
    bacteria_eastern, fungi_eastern,
    "Bacteria_Eastern", "Fungi_Eastern"
  )
}

# Generate all plots and outputs
cat("\n\n========== GENERATING PLOTS AND OUTPUTS ==========\n")

# Plot individual bacterial networks
cat("\n--- PLOTTING BACTERIAL NETWORKS ---\n")

if (exists("bacteria_total_network") && !is.null(bacteria_total_network)) {
  plot_individual_network(bacteria_total_network, "Bacteria_Total", TAXONOMIC_LEVEL)
}

if (exists("bacteria_sediment_network") && !is.null(bacteria_sediment_network)) {
  plot_individual_network(bacteria_sediment_network, "Bacteria_Sediment", TAXONOMIC_LEVEL)
}

if (exists("bacteria_water_network") && !is.null(bacteria_water_network)) {
  plot_individual_network(bacteria_water_network, "Bacteria_Water", TAXONOMIC_LEVEL)
}

if (exists("bacteria_western_network") && !is.null(bacteria_western_network)) {
  plot_individual_network(bacteria_western_network, "Bacteria_Western", TAXONOMIC_LEVEL)
}

if (exists("bacteria_eastern_network") && !is.null(bacteria_eastern_network)) {
  plot_individual_network(bacteria_eastern_network, "Bacteria_Eastern", TAXONOMIC_LEVEL)
}

# Plot individual fungal networks
cat("\n--- PLOTTING FUNGAL NETWORKS ---\n")

if (exists("fungi_total_network") && !is.null(fungi_total_network)) {
  plot_individual_network(fungi_total_network, "Fungi_Total", TAXONOMIC_LEVEL)
}

if (exists("fungi_sediment_network") && !is.null(fungi_sediment_network)) {
  plot_individual_network(fungi_sediment_network, "Fungi_Sediment", TAXONOMIC_LEVEL)
}

if (exists("fungi_water_network") && !is.null(fungi_water_network)) {
  plot_individual_network(fungi_water_network, "Fungi_Water", TAXONOMIC_LEVEL)
}

if (exists("fungi_western_network") && !is.null(fungi_western_network)) {
  plot_individual_network(fungi_western_network, "Fungi_Western", TAXONOMIC_LEVEL)
}

if (exists("fungi_eastern_network") && !is.null(fungi_eastern_network)) {
  plot_individual_network(fungi_eastern_network, "Fungi_Eastern", TAXONOMIC_LEVEL)
}

# Plot cross-correlation networks
cat("\n--- PLOTTING CROSS-CORRELATION NETWORKS ---\n")

if (exists("bacteria_fungi_total_network") && !is.null(bacteria_fungi_total_network)) {
  plot_cross_network(bacteria_fungi_total_network, "Bacteria_Total", "Fungi_Total", TAXONOMIC_LEVEL)
}

if (exists("bacteria_fungi_sediment_network") && !is.null(bacteria_fungi_sediment_network)) {
  plot_cross_network(bacteria_fungi_sediment_network, "Bacteria_Sediment", "Fungi_Sediment", TAXONOMIC_LEVEL)
}

if (exists("bacteria_fungi_water_network") && !is.null(bacteria_fungi_water_network)) {
  plot_cross_network(bacteria_fungi_water_network, "Bacteria_Water", "Fungi_Water", TAXONOMIC_LEVEL)
}

if (exists("bacteria_fungi_western_network") && !is.null(bacteria_fungi_western_network)) {
  plot_cross_network(bacteria_fungi_western_network, "Bacteria_Western", "Fungi_Western", TAXONOMIC_LEVEL)
}

if (exists("bacteria_fungi_eastern_network") && !is.null(bacteria_fungi_eastern_network)) {
  plot_cross_network(bacteria_fungi_eastern_network, "Bacteria_Eastern", "Fungi_Eastern", TAXONOMIC_LEVEL)
}

cat("\n\n========== ANALYSIS COMPLETED ==========\n")
cat("Taxonomic level:", TAXONOMIC_LEVEL, "\n")
cat("All network analyses and plots have been generated!\n")
cat("\nGenerated files:\n")
cat("- Individual bacterial networks (total, sediment, water, western, eastern)\n")
cat("- Individual fungal networks (total, sediment, water, western, eastern)\n")
cat("- Combined bacteria-fungi cross-networks (total, sediment, water, western, eastern)\n")
cat("\nAll plots are saved as PDF files in the current directory.\n")
