edge_colors <- adjustcolor(base_edge_colors, alpha.f = 0.7)  # 70% opacity for edges
# Create filename
filename <- paste0(group_name, "_", taxonomic_level, "_network_analysis.pdf")
# Create multi-panel plot
pdf(filename, width = 16, height = 12)
# Set up 2x2 layout
par(mfrow = c(2, 2), mar = c(4, 4, 3, 2))
# Plot 1: Main network
plot(network,
layout = layout,
vertex.color = node_colors,
vertex.size = node_sizes,
vertex.label = ifelse(network_result$node_degrees >= quantile(network_result$node_degrees, 0.9),
V(network)$name, ""),
vertex.label.cex = 0.7,
vertex.label.color = "black",
edge.color = edge_colors,
edge.width = abs(E(network)$correlation) * 2,
main = paste(group_name, "Network at", taxonomic_level, "Level"),
sub = paste("Nodes:", vcount(network), "| Edges:", ecount(network),
"| Communities:", network_result$community_count))
# Add legend to network plot
legend("topright",
legend = c("Positive correlation", "Negative correlation", "Hub nodes"),
col = c("#4682B4", "#CD5C5C", "black"),
lty = c(1, 1, NA),
pch = c(NA, NA, 16),
cex = 0.7)
# Plot 2: Degree distribution
degree_data <- network_result$node_degrees
hist(degree_data,
breaks = max(10, length(unique(degree_data))),
col = adjustcolor("#87CEEB", alpha.f = 0.8),
border = "black",
main = "Node Degree Distribution",
xlab = "Degree",
ylab = "Frequency")
# Add statistics to degree plot
abline(v = mean(degree_data), col = "red", lwd = 2, lty = 2)
legend("topright",
legend = paste("Mean:", round(mean(degree_data), 2)),
col = "red", lty = 2, lwd = 2, cex = 0.8)
# Plot 3: Betweenness distribution
betweenness_data <- network_result$betweenness
hist(betweenness_data,
breaks = max(10, length(unique(betweenness_data))),
col = adjustcolor("#98D982", alpha.f = 0.8),
border = "black",
main = "Betweenness Centrality Distribution",
xlab = "Betweenness Centrality",
ylab = "Frequency")
# Add statistics to betweenness plot
abline(v = mean(betweenness_data), col = "red", lwd = 2, lty = 2)
legend("topright",
legend = paste("Mean:", round(mean(betweenness_data), 2)),
col = "red", lty = 2, lwd = 2, cex = 0.8)
# Plot 4: Degree vs Betweenness scatter plot
plot(degree_data, betweenness_data,
pch = 16,
col = adjustcolor("#DDA0DD", alpha.f = 0.7),
cex = 1.2,
main = "Degree vs Betweenness Centrality",
xlab = "Degree",
ylab = "Betweenness Centrality")
# Add correlation line
if (length(degree_data) > 2) {
abline(lm(betweenness_data ~ degree_data), col = "red", lwd = 2)
correlation <- cor(degree_data, betweenness_data, use = "complete.obs")
legend("topright",
legend = paste("r =", round(correlation, 3)),
col = "red", lty = 1, lwd = 2, cex = 0.8)
}
dev.off()
cat("Network analysis plot saved:", filename, "\n")
}, error = function(e) {
cat("Error creating network plot:", e$message, "\n")
})
}
# Function to plot cross-correlation networks with degree and betweenness plots
plot_cross_network <- function(cross_network_result, group1_name, group2_name, taxonomic_level) {
if (is.null(cross_network_result)) {
cat("Cannot plot cross-network: network result is NULL\n")
return(NULL)
}
tryCatch({
network <- cross_network_result$network
# Set up layout for bipartite network
layout <- layout_as_bipartite(network)
# Color nodes by group with muted, transparent colors
base_node_colors <- ifelse(V(network)$type == group1_name, "#87CEEB", "#98D982")  # Muted sky blue and sage green
node_colors <- adjustcolor(base_node_colors, alpha.f = 0.8)  # 80% opacity
# Size nodes by degree
node_sizes <- (cross_network_result$node_degrees / max(cross_network_result$node_degrees)) * 12 + 4
# Color edges by correlation sign with muted, transparent colors
base_edge_colors <- ifelse(E(network)$sign == "positive", "#4682B4", "#CD5C5C")  # Steel blue and Indian red
edge_colors <- adjustcolor(base_edge_colors, alpha.f = 0.7)  # 70% opacity for edges
# Create filename
filename <- paste0(group1_name, "_vs_", group2_name, "_", taxonomic_level, "_cross_network_analysis.pdf")
# Create multi-panel plot
pdf(filename, width = 16, height = 12)
# Set up 2x2 layout
par(mfrow = c(2, 2), mar = c(4, 4, 3, 2))
# Plot 1: Main cross-network
plot(network,
layout = layout,
vertex.color = node_colors,
vertex.size = node_sizes,
vertex.label = ifelse(cross_network_result$node_degrees >= quantile(cross_network_result$node_degrees, 0.9),
V(network)$name, ""),
vertex.label.cex = 0.6,
vertex.label.color = "black",
edge.color = edge_colors,
edge.width = abs(E(network)$correlation) * 2,
main = paste(group1_name, "vs", group2_name, "Cross-Network"),
sub = paste("Nodes:", vcount(network), "| Edges:", ecount(network),
"| Communities:", cross_network_result$community_count))
# Add legend to network plot
legend("topright",
legend = c(group1_name, group2_name, "Positive corr.", "Negative corr."),
col = c("#87CEEB", "#98D982", "#4682B4", "#CD5C5C"),
pch = c(16, 16, NA, NA),
lty = c(NA, NA, 1, 1),
cex = 0.7)
# Plot 2: Degree distribution by group
degree_data <- cross_network_result$node_degrees
group1_degrees <- degree_data[V(network)$type == group1_name]
group2_degrees <- degree_data[V(network)$type == group2_name]
# Create side-by-side boxplot
boxplot(list(group1_degrees, group2_degrees),
names = c(group1_name, group2_name),
col = adjustcolor(c("#87CEEB", "#98D982"), alpha.f = 0.8),
main = "Degree Distribution by Group",
ylab = "Degree",
las = 2)
# Add mean lines
points(1, mean(group1_degrees), pch = 4, col = "red", cex = 2, lwd = 2)
points(2, mean(group2_degrees), pch = 4, col = "red", cex = 2, lwd = 2)
# Plot 3: Betweenness distribution by group
betweenness_data <- cross_network_result$betweenness
group1_betweenness <- betweenness_data[V(network)$type == group1_name]
group2_betweenness <- betweenness_data[V(network)$type == group2_name]
# Create side-by-side boxplot
boxplot(list(group1_betweenness, group2_betweenness),
names = c(group1_name, group2_name),
col = adjustcolor(c("#87CEEB", "#98D982"), alpha.f = 0.8),
main = "Betweenness Distribution by Group",
ylab = "Betweenness Centrality",
las = 2)
# Add mean lines
points(1, mean(group1_betweenness), pch = 4, col = "red", cex = 2, lwd = 2)
points(2, mean(group2_betweenness), pch = 4, col = "red", cex = 2, lwd = 2)
# Plot 4: Degree vs Betweenness scatter plot colored by group
plot(degree_data, betweenness_data,
pch = 16,
col = adjustcolor(base_node_colors, alpha.f = 0.7),
cex = 1.2,
main = "Degree vs Betweenness by Group",
xlab = "Degree",
ylab = "Betweenness Centrality")
# Add correlation line for each group
if (length(group1_degrees) > 2) {
abline(lm(group1_betweenness ~ group1_degrees), col = "#4682B4", lwd = 2)
}
if (length(group2_degrees) > 2) {
abline(lm(group2_betweenness ~ group2_degrees), col = "#CD5C5C", lwd = 2)
}
# Add legend with correlations
if (length(group1_degrees) > 2 && length(group2_degrees) > 2) {
cor1 <- cor(group1_degrees, group1_betweenness, use = "complete.obs")
cor2 <- cor(group2_degrees, group2_betweenness, use = "complete.obs")
legend("topright",
legend = c(paste(group1_name, "r =", round(cor1, 3)),
paste(group2_name, "r =", round(cor2, 3))),
col = c("#4682B4", "#CD5C5C"),
lty = 1, lwd = 2, cex = 0.7)
}
dev.off()
cat("Cross-network analysis plot saved:", filename, "\n")
}, error = function(e) {
cat("Error creating cross-network plot:", e$message, "\n")
})
}
# Function to save hub node analysis
save_hub_node_analysis <- function(network_result, group_name, taxonomic_level) {
if (is.null(network_result)) {
return(NULL)
}
tryCatch({
# Create hub node data frame
hub_data <- data.frame(
Taxon = network_result$hub_nodes,
Degree = network_result$node_degrees[network_result$hub_nodes],
Betweenness = network_result$betweenness[network_result$hub_nodes],
Closeness = network_result$closeness[network_result$hub_nodes],
Community = membership(network_result$communities)[network_result$hub_nodes],
stringsAsFactors = FALSE
)
# Sort by degree
hub_data <- hub_data[order(hub_data$Degree, decreasing = TRUE), ]
# Save to CSV
filename <- paste0(group_name, "_", taxonomic_level, "_hub_nodes.csv")
write.csv(hub_data, filename, row.names = FALSE)
cat("Hub node analysis saved:", filename, "\n")
return(hub_data)
}, error = function(e) {
cat("Error saving hub node analysis:", e$message, "\n")
return(NULL)
})
}
# Function to save community analysis
save_community_analysis <- function(network_result, group_name, taxonomic_level) {
if (is.null(network_result)) {
return(NULL)
}
tryCatch({
# Create community data frame
community_data <- data.frame(
Taxon = network_result$taxa_names,
Community = membership(network_result$communities),
Degree = network_result$node_degrees,
Betweenness = network_result$betweenness,
Closeness = network_result$closeness,
stringsAsFactors = FALSE
)
# Sort by community and degree
community_data <- community_data[order(community_data$Community, community_data$Degree, decreasing = c(FALSE, TRUE)), ]
# Save to CSV
filename <- paste0(group_name, "_", taxonomic_level, "_communities.csv")
write.csv(community_data, filename, row.names = FALSE)
cat("Community analysis saved:", filename, "\n")
return(community_data)
}, error = function(e) {
cat("Error saving community analysis:", e$message, "\n")
return(NULL)
})
}
# Function to create network comparison table
create_network_comparison_table <- function(network_results_list, taxonomic_level) {
tryCatch({
comparison_data <- data.frame(
Network = character(),
Nodes = numeric(),
Edges = numeric(),
Avg_Degree = numeric(),
Max_Degree = numeric(),
Communities = numeric(),
Modularity = numeric(),
Hub_Nodes = numeric(),
Density = numeric(),
stringsAsFactors = FALSE
)
for (name in names(network_results_list)) {
result <- network_results_list[[name]]
if (!is.null(result)) {
comparison_data <- rbind(comparison_data, data.frame(
Network = name,
Nodes = vcount(result$network),
Edges = ecount(result$network),
Avg_Degree = mean(result$node_degrees),
Max_Degree = max(result$node_degrees),
Communities = result$community_count,
Modularity = result$modularity,
Hub_Nodes = length(result$hub_nodes),
Density = edge_density(result$network),
stringsAsFactors = FALSE
))
}
}
# Save comparison table
filename <- paste0("Network_Comparison_", taxonomic_level, "_Level.csv")
write.csv(comparison_data, filename, row.names = FALSE)
cat("Network comparison table saved:", filename, "\n")
return(comparison_data)
}, error = function(e) {
cat("Error creating comparison table:", e$message, "\n")
return(NULL)
})
}
# Perform individual network analyses
cat("\n\n========== INDIVIDUAL NETWORK ANALYSES ==========\n")
# 1. Single bacterial networks
cat("\n--- BACTERIAL NETWORKS ---\n")
# Total bacterial network
bacteria_total_cor <- create_taxonomic_correlation_matrix(bacteria_agg, threshold = 0.6)
bacteria_total_network <- analyze_taxonomic_network(bacteria_total_cor, "Bacteria_Total")
# Bacterial networks by Sample_type
if (!is.null(bacteria_sediment)) {
bacteria_sediment_cor <- create_taxonomic_correlation_matrix(bacteria_sediment, threshold = 0.6)
bacteria_sediment_network <- analyze_taxonomic_network(bacteria_sediment_cor, "Bacteria_Sediment")
}
if (!is.null(bacteria_water)) {
bacteria_water_cor <- create_taxonomic_correlation_matrix(bacteria_water, threshold = 0.6)
bacteria_water_network <- analyze_taxonomic_network(bacteria_water_cor, "Bacteria_Water")
}
# Bacterial networks by Region
if (!is.null(bacteria_western)) {
bacteria_western_cor <- create_taxonomic_correlation_matrix(bacteria_western, threshold = 0.6)
bacteria_western_network <- analyze_taxonomic_network(bacteria_western_cor, "Bacteria_Western")
}
if (!is.null(bacteria_eastern)) {
bacteria_eastern_cor <- create_taxonomic_correlation_matrix(bacteria_eastern, threshold = 0.6)
bacteria_eastern_network <- analyze_taxonomic_network(bacteria_eastern_cor, "Bacteria_Eastern")
}
# 2. Single fungal networks
cat("\n--- FUNGAL NETWORKS ---\n")
# Total fungal network
fungi_total_cor <- create_taxonomic_correlation_matrix(fungi_agg, threshold = 0.6)
fungi_total_network <- analyze_taxonomic_network(fungi_total_cor, "Fungi_Total")
# Fungal networks by Sample_type
if (!is.null(fungi_sediment)) {
fungi_sediment_cor <- create_taxonomic_correlation_matrix(fungi_sediment, threshold = 0.6)
fungi_sediment_network <- analyze_taxonomic_network(fungi_sediment_cor, "Fungi_Sediment")
}
if (!is.null(fungi_water)) {
fungi_water_cor <- create_taxonomic_correlation_matrix(fungi_water, threshold = 0.6)
fungi_water_network <- analyze_taxonomic_network(fungi_water_cor, "Fungi_Water")
}
# Fungal networks by Region
if (!is.null(fungi_western)) {
fungi_western_cor <- create_taxonomic_correlation_matrix(fungi_western, threshold = 0.6)
fungi_western_network <- analyze_taxonomic_network(fungi_western_cor, "Fungi_Western")
}
if (!is.null(fungi_eastern)) {
fungi_eastern_cor <- create_taxonomic_correlation_matrix(fungi_eastern, threshold = 0.6)
fungi_eastern_network <- analyze_taxonomic_network(fungi_eastern_cor, "Fungi_Eastern")
}
# Perform cross-correlation analyses (bacteria vs fungi)
cat("\n\n========== CROSS-CORRELATION ANALYSES ==========\n")
# Helper function to perform cross-correlation with multiple thresholds
perform_taxonomic_cross_correlation <- function(table1, table2, group1_name, group2_name) {
cat("\n\n----- ", group1_name, " vs ", group2_name, " -----\n", sep="")
# For cross-correlations, start with lower threshold (0.4) since cross-kingdom correlations are typically weaker
tryCatch({
cross_cor <- create_taxonomic_cross_correlation_matrix(table1, table2, threshold = 0.4)
if (!is.null(cross_cor) && sum(cross_cor$cor_matrix != 0) > 0) {
cat("\nFound correlations above threshold 0.4\n")
cross_network <- analyze_taxonomic_cross_network(cross_cor, group1_name, group2_name)
return(cross_network)
} else {
cat("\nNo correlations above threshold 0.4. Trying threshold 0.3...\n")
# Try with threshold 0.3
cross_cor_03 <- create_taxonomic_cross_correlation_matrix(table1, table2, threshold = 0.3)
if (!is.null(cross_cor_03) && sum(cross_cor_03$cor_matrix != 0) > 0) {
cat("\nFound correlations above threshold 0.3\n")
cross_network <- analyze_taxonomic_cross_network(cross_cor_03,
paste(group1_name, "(0.3)"),
paste(group2_name, "(0.3)"))
return(cross_network)
} else {
cat("\nNo correlations above threshold 0.3. Trying threshold 0.25...\n")
# Try with threshold 0.25
cross_cor_025 <- create_taxonomic_cross_correlation_matrix(table1, table2, threshold = 0.25)
if (!is.null(cross_cor_025) && sum(cross_cor_025$cor_matrix != 0) > 0) {
cat("\nFound correlations above threshold 0.25\n")
cross_network <- analyze_taxonomic_cross_network(cross_cor_025,
paste(group1_name, "(0.25)"),
paste(group2_name, "(0.25)"))
return(cross_network)
} else {
cat("\nNo significant cross-correlations found even at threshold 0.25\n")
cat("This may indicate weak bacteria-fungi interactions in this environment\n")
return(NULL)
}
}
}
}, error = function(e) {
cat("Error in cross-correlation analysis:", e$message, "\n")
return(NULL)
})
}
# 1. Total bacteria vs fungi cross-network
bacteria_fungi_total_network <- perform_taxonomic_cross_correlation(
bacteria_agg, fungi_agg,
"Bacteria_Total", "Fungi_Total"
)
# 2. Cross-networks by Sample_type
if (!is.null(bacteria_sediment) && !is.null(fungi_sediment)) {
bacteria_fungi_sediment_network <- perform_taxonomic_cross_correlation(
bacteria_sediment, fungi_sediment,
"Bacteria_Sediment", "Fungi_Sediment"
)
}
if (!is.null(bacteria_water) && !is.null(fungi_water)) {
bacteria_fungi_water_network <- perform_taxonomic_cross_correlation(
bacteria_water, fungi_water,
"Bacteria_Water", "Fungi_Water"
)
}
# 3. Cross-networks by Region
if (!is.null(bacteria_western) && !is.null(fungi_western)) {
bacteria_fungi_western_network <- perform_taxonomic_cross_correlation(
bacteria_western, fungi_western,
"Bacteria_Western", "Fungi_Western"
)
}
if (!is.null(bacteria_eastern) && !is.null(fungi_eastern)) {
bacteria_fungi_eastern_network <- perform_taxonomic_cross_correlation(
bacteria_eastern, fungi_eastern,
"Bacteria_Eastern", "Fungi_Eastern"
)
}
# Generate all plots and outputs
cat("\n\n========== GENERATING PLOTS AND OUTPUTS ==========\n")
# Plot individual bacterial networks and save analyses
cat("\n--- PLOTTING BACTERIAL NETWORKS ---\n")
# Store individual networks for comparison
individual_networks <- list()
if (exists("bacteria_total_network") && !is.null(bacteria_total_network)) {
plot_individual_network(bacteria_total_network, "Bacteria_Total", TAXONOMIC_LEVEL)
save_hub_node_analysis(bacteria_total_network, "Bacteria_Total", TAXONOMIC_LEVEL)
save_community_analysis(bacteria_total_network, "Bacteria_Total", TAXONOMIC_LEVEL)
individual_networks[["Bacteria_Total"]] <- bacteria_total_network
}
if (exists("bacteria_sediment_network") && !is.null(bacteria_sediment_network)) {
plot_individual_network(bacteria_sediment_network, "Bacteria_Sediment", TAXONOMIC_LEVEL)
save_hub_node_analysis(bacteria_sediment_network, "Bacteria_Sediment", TAXONOMIC_LEVEL)
save_community_analysis(bacteria_sediment_network, "Bacteria_Sediment", TAXONOMIC_LEVEL)
individual_networks[["Bacteria_Sediment"]] <- bacteria_sediment_network
}
if (exists("bacteria_water_network") && !is.null(bacteria_water_network)) {
plot_individual_network(bacteria_water_network, "Bacteria_Water", TAXONOMIC_LEVEL)
save_hub_node_analysis(bacteria_water_network, "Bacteria_Water", TAXONOMIC_LEVEL)
save_community_analysis(bacteria_water_network, "Bacteria_Water", TAXONOMIC_LEVEL)
individual_networks[["Bacteria_Water"]] <- bacteria_water_network
}
if (exists("bacteria_western_network") && !is.null(bacteria_western_network)) {
plot_individual_network(bacteria_western_network, "Bacteria_Western", TAXONOMIC_LEVEL)
save_hub_node_analysis(bacteria_western_network, "Bacteria_Western", TAXONOMIC_LEVEL)
save_community_analysis(bacteria_western_network, "Bacteria_Western", TAXONOMIC_LEVEL)
individual_networks[["Bacteria_Western"]] <- bacteria_western_network
}
if (exists("bacteria_eastern_network") && !is.null(bacteria_eastern_network)) {
plot_individual_network(bacteria_eastern_network, "Bacteria_Eastern", TAXONOMIC_LEVEL)
save_hub_node_analysis(bacteria_eastern_network, "Bacteria_Eastern", TAXONOMIC_LEVEL)
save_community_analysis(bacteria_eastern_network, "Bacteria_Eastern", TAXONOMIC_LEVEL)
individual_networks[["Bacteria_Eastern"]] <- bacteria_eastern_network
}
# Plot individual fungal networks and save analyses
cat("\n--- PLOTTING FUNGAL NETWORKS ---\n")
if (exists("fungi_total_network") && !is.null(fungi_total_network)) {
plot_individual_network(fungi_total_network, "Fungi_Total", TAXONOMIC_LEVEL)
save_hub_node_analysis(fungi_total_network, "Fungi_Total", TAXONOMIC_LEVEL)
save_community_analysis(fungi_total_network, "Fungi_Total", TAXONOMIC_LEVEL)
individual_networks[["Fungi_Total"]] <- fungi_total_network
}
if (exists("fungi_sediment_network") && !is.null(fungi_sediment_network)) {
plot_individual_network(fungi_sediment_network, "Fungi_Sediment", TAXONOMIC_LEVEL)
save_hub_node_analysis(fungi_sediment_network, "Fungi_Sediment", TAXONOMIC_LEVEL)
save_community_analysis(fungi_sediment_network, "Fungi_Sediment", TAXONOMIC_LEVEL)
individual_networks[["Fungi_Sediment"]] <- fungi_sediment_network
}
if (exists("fungi_water_network") && !is.null(fungi_water_network)) {
plot_individual_network(fungi_water_network, "Fungi_Water", TAXONOMIC_LEVEL)
save_hub_node_analysis(fungi_water_network, "Fungi_Water", TAXONOMIC_LEVEL)
save_community_analysis(fungi_water_network, "Fungi_Water", TAXONOMIC_LEVEL)
individual_networks[["Fungi_Water"]] <- fungi_water_network
}
if (exists("fungi_western_network") && !is.null(fungi_western_network)) {
plot_individual_network(fungi_western_network, "Fungi_Western", TAXONOMIC_LEVEL)
save_hub_node_analysis(fungi_western_network, "Fungi_Western", TAXONOMIC_LEVEL)
save_community_analysis(fungi_western_network, "Fungi_Western", TAXONOMIC_LEVEL)
individual_networks[["Fungi_Western"]] <- fungi_western_network
}
if (exists("fungi_eastern_network") && !is.null(fungi_eastern_network)) {
plot_individual_network(fungi_eastern_network, "Fungi_Eastern", TAXONOMIC_LEVEL)
save_hub_node_analysis(fungi_eastern_network, "Fungi_Eastern", TAXONOMIC_LEVEL)
save_community_analysis(fungi_eastern_network, "Fungi_Eastern", TAXONOMIC_LEVEL)
individual_networks[["Fungi_Eastern"]] <- fungi_eastern_network
}
# Plot cross-correlation networks and save analyses
cat("\n--- PLOTTING CROSS-CORRELATION NETWORKS ---\n")
# Store cross networks for comparison
cross_networks <- list()
if (exists("bacteria_fungi_total_network") && !is.null(bacteria_fungi_total_network)) {
plot_cross_network(bacteria_fungi_total_network, "Bacteria_Total", "Fungi_Total", TAXONOMIC_LEVEL)
save_hub_node_analysis(bacteria_fungi_total_network, "Bacteria_Fungi_Total", TAXONOMIC_LEVEL)
cross_networks[["Bacteria_Fungi_Total"]] <- bacteria_fungi_total_network
}
if (exists("bacteria_fungi_sediment_network") && !is.null(bacteria_fungi_sediment_network)) {
plot_cross_network(bacteria_fungi_sediment_network, "Bacteria_Sediment", "Fungi_Sediment", TAXONOMIC_LEVEL)
save_hub_node_analysis(bacteria_fungi_sediment_network, "Bacteria_Fungi_Sediment", TAXONOMIC_LEVEL)
cross_networks[["Bacteria_Fungi_Sediment"]] <- bacteria_fungi_sediment_network
}
if (exists("bacteria_fungi_water_network") && !is.null(bacteria_fungi_water_network)) {
plot_cross_network(bacteria_fungi_water_network, "Bacteria_Water", "Fungi_Water", TAXONOMIC_LEVEL)
save_hub_node_analysis(bacteria_fungi_water_network, "Bacteria_Fungi_Water", TAXONOMIC_LEVEL)
cross_networks[["Bacteria_Fungi_Water"]] <- bacteria_fungi_water_network
}
if (exists("bacteria_fungi_western_network") && !is.null(bacteria_fungi_western_network)) {
plot_cross_network(bacteria_fungi_western_network, "Bacteria_Western", "Fungi_Western", TAXONOMIC_LEVEL)
save_hub_node_analysis(bacteria_fungi_western_network, "Bacteria_Fungi_Western", TAXONOMIC_LEVEL)
cross_networks[["Bacteria_Fungi_Western"]] <- bacteria_fungi_western_network
}
if (exists("bacteria_fungi_eastern_network") && !is.null(bacteria_fungi_eastern_network)) {
plot_cross_network(bacteria_fungi_eastern_network, "Bacteria_Eastern", "Fungi_Eastern", TAXONOMIC_LEVEL)
save_hub_node_analysis(bacteria_fungi_eastern_network, "Bacteria_Fungi_Eastern", TAXONOMIC_LEVEL)
cross_networks[["Bacteria_Fungi_Eastern"]] <- bacteria_fungi_eastern_network
}
# Create comparison tables
cat("\n--- CREATING COMPARISON TABLES ---\n")
if (length(individual_networks) > 0) {
individual_comparison <- create_network_comparison_table(individual_networks, paste0(TAXONOMIC_LEVEL, "_Individual"))
}
if (length(cross_networks) > 0) {
cross_comparison <- create_network_comparison_table(cross_networks, paste0(TAXONOMIC_LEVEL, "_Cross"))
}
cat("\n\n========== ANALYSIS COMPLETED ==========\n")
cat("Taxonomic level:", TAXONOMIC_LEVEL, "\n")
cat("Individual networks analyzed:", length(individual_networks), "\n")
cat("Cross-correlation networks analyzed:", length(cross_networks), "\n")
cat("\nGenerated files:\n")
cat("- Individual bacterial networks (total, sediment, water, western, eastern)\n")
